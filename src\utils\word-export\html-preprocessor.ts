/**
 * HTML预处理工具
 * 处理html-to-docx无法很好处理的HTML元素和样式
 */
import { JSDOM } from 'jsdom';

export interface PreprocessOptions {
  /** 是否处理着重号 */
  processEmphasisMarks?: boolean;
  /** 是否处理波浪线 */
  processWavyUnderlines?: boolean;
  /** 是否处理下横线 */
  processUnderlines?: boolean;
  /** 是否处理display:inline-block元素和flex容器 */
  processInlineBlocks?: boolean;
  /** 是否处理浮动元素 */
  processFloatingElements?: boolean;
  /** 是否优化表格样式 */
  optimizeTableStyles?: boolean;
  /** 是否处理图片 */
  processImages?: boolean;
}

/**
 * HTML预处理器
 */
export class HtmlPreprocessor {
  private dom: JSDOM;
  private document: Document;

  constructor(htmlContent: string) {
    this.dom = new JSDOM(htmlContent);
    this.document = this.dom.window.document;
  }

  /**
   * 执行预处理
   */
  preprocess(options: PreprocessOptions = {}): string {
    const {
      processEmphasisMarks = true,
      processWavyUnderlines = true,
      processUnderlines = true,
      processInlineBlocks = true,
      processFloatingElements = true,
      optimizeTableStyles = true,
      processImages = true,
    } = options;

    console.log('开始HTML预处理...');

    if (processEmphasisMarks) {
      this.processEmphasisMarks();
    }

    if (processWavyUnderlines) {
      this.processWavyUnderlines();
    }

    if (processUnderlines) {
      this.processUnderlines();
    }

    if (processImages) {
      this.processImages();
    }

    if (processInlineBlocks) {
      this.processInlineBlockElements();
    }

    if (processFloatingElements) {
      this.removeFloatingElements();
    }

    if (optimizeTableStyles) {
      this.optimizeTableStyles();
    }

    // 清理可能导致问题的样式
    this.cleanupProblematicStyles();

    console.log('HTML预处理完成');
    return this.getProcessedHtml();
  }

  /**
   * 处理着重号
   * 使用特殊标记方案：将着重号转换为特殊标记，供后续docx库处理
   */
  private processEmphasisMarks(): void {
    console.log('处理着重号：转换为特殊标记...');

    // 查找所有带有着重号样式的元素
    const emphasisElements = this.document.querySelectorAll(
      '[data-emphasis-mark="dot"], .emphasis-mark, [style*="text-emphasis"]'
    );

    let processedCount = 0;

    emphasisElements.forEach(element => {
      const htmlElement = element as HTMLElement;

      // 查找包含实际文本内容的子元素（通常是内层span）
      const textElements = htmlElement.querySelectorAll('span');
      let targetElement: HTMLElement | null = null;

      // 如果有子元素，找到包含文本且有字体样式的元素
      if (textElements.length > 0) {
        for (const textEl of textElements) {
          const textEl_html = textEl as HTMLElement;
          const hasText = textEl_html.textContent?.trim();
          const hasFont =
            textEl_html.style.fontFamily || textEl_html.style.fontSize;

          if (hasText && hasFont) {
            targetElement = textEl_html;
            break;
          }
        }
      }

      // 如果没有找到合适的子元素，使用当前元素
      if (!targetElement) {
        targetElement = htmlElement;
      }

      const textContent = targetElement.textContent?.trim();

      if (textContent) {
        // 保留原有样式，只修改文本内容为特殊标记格式
        targetElement.textContent = `[EMPHASIS]${textContent}[/EMPHASIS]`;

        // 只从外层元素移除着重号相关的特定样式和属性，保留内层元素的字体样式
        htmlElement.removeAttribute('data-emphasis-mark');
        htmlElement.classList.remove('emphasis-mark');
        htmlElement.style.removeProperty('text-emphasis');
        htmlElement.style.removeProperty('text-emphasis-position');
        // 注意：不移除text-decoration和border-bottom，因为可能是其他样式需要的

        processedCount++;
      }
    });

    if (processedCount > 0) {
      console.log(
        `着重号预处理完成，转换了 ${processedCount} 个着重号为特殊标记`
      );
    }
  }

  /**
   * 处理波浪线下划线
   * 使用特殊标记方案：将波浪线转换为特殊标记，供后续docx库处理
   */
  private processWavyUnderlines(): void {
    console.log('处理波浪线：转换为特殊标记...');

    // 查找所有带有波浪线样式的元素
    const wavyElements = this.document.querySelectorAll(
      '[style*="text-decoration: underline wavy"], [style*="text-decoration:underline wavy"], .wavy-underline'
    );

    let processedCount = 0;

    wavyElements.forEach(element => {
      const htmlElement = element as HTMLElement;

      // 查找包含实际文本内容的子元素（通常是内层span）
      const textElements = htmlElement.querySelectorAll('span');
      let targetElement: HTMLElement | null = null;

      // 如果有子元素，找到包含文本且有字体样式的元素
      if (textElements.length > 0) {
        for (const textEl of textElements) {
          const textEl_html = textEl as HTMLElement;
          const hasText = textEl_html.textContent?.trim();
          const hasFont =
            textEl_html.style.fontFamily || textEl_html.style.fontSize;

          if (hasText && hasFont) {
            targetElement = textEl_html;
            break;
          }
        }
      }

      // 如果没有找到合适的子元素，使用当前元素
      if (!targetElement) {
        targetElement = htmlElement;
      }

      const textContent = targetElement.textContent?.trim();

      if (textContent) {
        // 保留原有样式，只修改文本内容为特殊标记格式
        targetElement.textContent = `[WAVY]${textContent}[/WAVY]`;

        // 只从外层元素移除波浪线相关的特定样式和属性，保留内层元素的字体样式
        htmlElement.classList.remove('wavy-underline');
        // 只移除波浪线相关的text-decoration，但保留其他样式
        const textDecoration = htmlElement.style.textDecoration;
        if (textDecoration && textDecoration.includes('wavy')) {
          htmlElement.style.removeProperty('text-decoration');
        }

        processedCount++;
      }
    });

    if (processedCount > 0) {
      console.log(
        `波浪线预处理完成，转换了 ${processedCount} 个波浪线为特殊标记`
      );
    }
  }

  /**
   * 处理下横线
   * 使用特殊标记方案：将style="text-decoration: underline"的下横线转换为特殊标记
   * 因为html-to-docx无法正确处理style属性的下划线，只能处理<u>标签
   */
  private processUnderlines(): void {
    console.log('处理下横线：转换为特殊标记...');

    // 查找所有带有text-decoration: underline样式的元素
    const underlineElements = this.document.querySelectorAll(
      '[style*="text-decoration: underline"], [style*="text-decoration:underline"]'
    );

    let processedCount = 0;

    underlineElements.forEach(element => {
      const htmlElement = element as HTMLElement;

      // 检查是否确实包含下划线样式
      const textDecoration = htmlElement.style.textDecoration;
      if (!textDecoration || !textDecoration.includes('underline')) {
        return;
      }

      // 获取元素的文本内容
      const textContent = htmlElement.textContent || '';

      // 只处理包含连续空格的下横线（用于填空）
      // 排除普通文本的下划线
      if (!/\s{3,}/.test(textContent)) {
        return;
      }

      console.log(`发现下横线空格: "${textContent}" (${textContent.length}个字符)`);

      // 使用文本标记保留下横线样式和nbsp个数
      // 格式：[UNDERLINE]原始内容[/UNDERLINE]
      const marker = `[UNDERLINE]${textContent}[/UNDERLINE]`;

      // 创建新的文本节点替换原元素
      const textNode = this.document.createTextNode(marker);

      // 替换原元素
      if (htmlElement.parentNode) {
        htmlElement.parentNode.replaceChild(textNode, htmlElement);
        processedCount++;
      }
    });

    if (processedCount > 0) {
      console.log(
        `下横线预处理完成，转换了 ${processedCount} 个下横线为特殊标记`
      );
    }
  }

  /**
   * 处理图片
   * 优化图片尺寸，确保不超出页面宽度
   */
  private processImages(): void {
    console.log('处理图片：优化尺寸和格式...');

    const images = this.document.querySelectorAll('img');
    let processedCount = 0;

    // A4页面宽度约为210mm，减去页边距约为120mm，转换为像素约为450px
    const maxWidth = 450;

    images.forEach(img => {
      const htmlImg = img as HTMLImageElement;

      // 确保base64和SVG图片有正确的格式
      const src = htmlImg.src;
      if (src && (src.startsWith('data:image/') || src.includes('svg'))) {
        console.log(
          `发现${src.startsWith('data:image/') ? 'base64' : 'SVG'}图片`
        );

        // 检查是否是极小的图片（1x1像素等），需要特殊处理以确保在Word中正确显示
        if (src.startsWith('data:image/') && src.length < 200) {
          console.log(
            `检测到极小图片（${src.length}字符），可能是1x1像素图片，需要特殊处理`
          );

          // 获取图片的显示尺寸
          const width = htmlImg.width || 100;
          const height = htmlImg.height || 100;

          // 创建一个可见的彩色方块来替代1x1像素图片
          // 使用SVG创建一个黄色方块
          const svgData = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
          </svg>`;
          const svgBase64 = btoa(svgData);
          const svgDataUrl = `data:image/svg+xml;base64,${svgBase64}`;

          // 替换图片源
          htmlImg.src = svgDataUrl;
          htmlImg.width = width;
          htmlImg.height = height;

          // 确保图片有明确的样式
          const currentStyle = htmlImg.getAttribute('style') || '';
          htmlImg.setAttribute(
            'style',
            `${currentStyle}; width: ${width}px; height: ${height}px; display: inline-block;`
          );

          console.log(`极小图片已替换为可见的${width}x${height}黄色方块`);
          processedCount++;
        }

        // 检查图片是否应该保持行内显示
        const shouldKeepInline = this.shouldKeepImageInline(htmlImg);

        // 检查图片是否嵌套在段落中
        const parentP = htmlImg.closest('p');
        if (parentP) {
          if (shouldKeepInline) {
            console.log('发现应该行内显示的图片，使用表格方式强制同行显示');

            // 找到最近的flex容器
            let flexContainer: Element | null = parentP;
            while (flexContainer && flexContainer.parentElement) {
              const style = flexContainer.getAttribute('style') || '';
              if (style.includes('display: flex') || style.includes('display:flex')) {
                break;
              }
              flexContainer = flexContainer.parentElement;
            }

            if (flexContainer) {
              // 获取flex容器的所有子元素
              const flexChildren = Array.from(flexContainer.children);

              // 找到题目序号元素
              let numberElement: Element | null = null;
              let contentElement: Element | null = null;

              for (const child of flexChildren) {
                const text = child.textContent?.trim() || '';
                if (/^\d+[）)]\s*\.$/.test(text)) {
                  numberElement = child;
                } else if (child.contains(htmlImg)) {
                  contentElement = child;
                }
              }

              if (numberElement && contentElement) {
                // 创建一个表格来强制同行显示
                const table = this.document.createElement('table');
                table.style.width = '100%';
                table.style.borderCollapse = 'collapse';
                table.style.border = 'none';
                table.style.margin = '0';
                table.style.padding = '0';

                const tbody = this.document.createElement('tbody');
                const tr = this.document.createElement('tr');

                // 第一个单元格：题目序号
                const td1 = this.document.createElement('td');
                td1.style.border = 'none';
                td1.style.padding = '0';
                td1.style.margin = '0';
                td1.style.verticalAlign = 'middle';
                td1.style.width = 'auto';
                td1.innerHTML = numberElement.innerHTML;

                // 第二个单元格：图片和内容
                const td2 = this.document.createElement('td');
                td2.style.border = 'none';
                td2.style.padding = '0 0 0 8px'; // 左边距8px模拟gap
                td2.style.margin = '0';
                td2.style.verticalAlign = 'middle';
                td2.style.width = '100%';

                // 设置图片样式
                htmlImg.style.display = 'inline';
                htmlImg.style.verticalAlign = 'middle';
                htmlImg.style.margin = '0 4px 0 0';
                htmlImg.style.maxHeight = '1.2em';

                // 将图片和其他内容放入第二个单元格
                td2.appendChild(htmlImg);

                // 如果还有其他内容，也添加进去
                const otherContent = contentElement.textContent?.trim();
                if (otherContent) {
                  const textSpan = this.document.createElement('span');
                  textSpan.textContent = otherContent;
                  td2.appendChild(textSpan);
                }

                tr.appendChild(td1);
                tr.appendChild(td2);
                tbody.appendChild(tr);
                table.appendChild(tbody);

                // 替换原来的flex容器
                flexContainer.parentNode?.replaceChild(table, flexContainer);

                processedCount++;
                console.log('图片已使用表格方式设置为同行显示');
              }
            }
          } else {
            console.log('发现嵌套在段落中的图片，提取为独立元素');

            // 创建一个新的div来包含图片
            const imgDiv = this.document.createElement('div');
            imgDiv.style.textAlign = 'center'; // 居中显示图片
            imgDiv.style.margin = '10px 0'; // 添加上下边距

            // 克隆图片元素
            const newImg = htmlImg.cloneNode(true) as HTMLImageElement;
            imgDiv.appendChild(newImg);

            // 在段落后插入新的图片div
            parentP.parentNode?.insertBefore(imgDiv, parentP.nextSibling);

            // 从原段落中移除图片
            htmlImg.remove();

            processedCount++;
            console.log('图片已提取为独立元素');
          }
        } else {
          console.log('图片不在段落中，保持原状');
        }

        // 对于base64图片，只通过CSS样式控制尺寸，不修改width/height属性
        // 这样可以避免影响html-to-docx的处理
        const currentStyle = htmlImg.getAttribute('style') || '';
        if (!currentStyle.includes('max-width')) {
          htmlImg.setAttribute(
            'style',
            `${currentStyle}; max-width: ${maxWidth}px; height: auto;`
          );
        }

        // 记录但不修改width/height属性，让html-to-docx自己处理
        if (htmlImg.width && htmlImg.width > maxWidth) {
          console.log(`图片宽度 ${htmlImg.width}px 超过限制，通过CSS样式控制`);
        }
      } else {
        // 对于非base64图片，可以正常处理尺寸
        if (htmlImg.width && htmlImg.width > maxWidth) {
          const ratio = maxWidth / htmlImg.width;
          htmlImg.width = maxWidth;
          if (htmlImg.height) {
            htmlImg.height = Math.round(htmlImg.height * ratio);
          }
          processedCount++;
        }

        // 设置最大宽度样式，防止图片过大
        const currentStyle = htmlImg.getAttribute('style') || '';
        if (!currentStyle.includes('max-width')) {
          htmlImg.setAttribute(
            'style',
            `${currentStyle}; max-width: ${maxWidth}px; height: auto;`
          );
        }
      }
    });

    if (processedCount > 0) {
      console.log(`图片预处理完成，优化了 ${processedCount} 个图片的尺寸`);
    }
  }

  /**
   * 处理display:inline-block元素和flex容器
   * 确保这些元素在Word中正确显示
   */
  private processInlineBlockElements(): void {
    console.log('处理inline-block元素和flex容器...');

    const allElements = this.document.querySelectorAll('*');
    let inlineBlockCount = 0;
    let flexContainerCount = 0;

    allElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      if (style.display === 'inline-block') {
        // 保持inline-block显示，不进行强制转换
        // inline-block在html-to-docx中通常能正确处理
        // 移除之前的强制转换逻辑，因为它会破坏原有布局
        inlineBlockCount++;
      } else if (style.display === 'flex') {
        // 处理flex容器：智能判断是否应该内联处理
        console.log('发现flex容器，处理其子元素...');

        // 获取第一级子元素（直接子元素）
        const directChildren = Array.from(htmlElement.children);

        // 判断这是否是应该内联的flex容器（如大题标题）
        const shouldInlineChildren = this.shouldInlineFlexChildren(directChildren);

        if (shouldInlineChildren) {
          // 只对应该内联的flex容器进行处理
          directChildren.forEach(child => {
            const childElement = child as HTMLElement;
            const childStyle = childElement.style;

            // 清理子元素中的多余空白
            this.cleanElementWhitespace(childElement);

            // 将子元素设置为内联显示，避免换行
            if (
              childStyle.display !== 'inline' &&
              childStyle.display !== 'inline-block'
            ) {
              childStyle.display = 'inline-block';
              console.log(
                `flex容器子元素已设置为inline-block: ${childElement.tagName}`
              );
            }

            // 确保子元素不会强制换行
            childStyle.whiteSpace = 'nowrap';
          });
          console.log(`处理了内联flex容器，包含 ${directChildren.length} 个子元素`);
        } else {
          // 对于包含长内容的flex容器，保持原有的块级布局
          console.log(`跳过长内容flex容器，包含 ${directChildren.length} 个子元素`);
        }

        flexContainerCount++;
      }
    });

    if (inlineBlockCount > 0) {
      console.log(`处理了 ${inlineBlockCount} 个inline-block元素`);
    }

    if (flexContainerCount > 0) {
      console.log(`处理了 ${flexContainerCount} 个flex容器`);
    }
  }

  /**
   * 清理元素中的多余空白
   * 移除flex容器子元素中的缩进空白，但保留有意义的空格内容
   */
  private cleanElementWhitespace(element: HTMLElement): void {
    // 检查元素是否包含有意义的空格内容（如下横线）
    if (this.hasSignificantWhitespace(element)) {
      // 如果包含有意义的空格，不进行清理
      return;
    }

    // 遍历元素的所有子节点
    const childNodes = Array.from(element.childNodes);

    childNodes.forEach(node => {
      if (node.nodeType === 3) { // TEXT_NODE = 3
        const textNode = node as Text;
        const originalText = textNode.textContent || '';

        // 只清理纯粹的格式化空白（换行、缩进等）
        // 保留有内容的文本和有意义的空格
        if (this.isPureFormattingWhitespace(originalText)) {
          const cleanedText = originalText.trim();
          if (cleanedText !== originalText) {
            textNode.textContent = cleanedText;
          }
        }
      } else if (node.nodeType === 1) { // ELEMENT_NODE = 1
        // 递归清理子元素
        this.cleanElementWhitespace(node as HTMLElement);
      }
    });
  }

  /**
   * 检查元素是否包含有意义的空格内容
   * 如用于下横线的连续空格、特殊格式等
   */
  private hasSignificantWhitespace(element: HTMLElement): boolean {
    // 检查元素的innerHTML是否包含连续的&nbsp;
    const innerHTML = element.innerHTML;

    // 检查是否包含连续的&nbsp;（通常用于下横线）
    if (/(&nbsp;){3,}/.test(innerHTML)) {
      return true;
    }

    // 检查是否有下划线样式（另一种下横线实现方式）
    if (element.style.textDecoration && element.style.textDecoration.includes('underline')) {
      return true;
    }

    // 检查是否包含特殊的空格字符（但排除纯HTML格式化空白）
    const textContent = element.textContent || '';

    // 如果文本内容很短且主要是空格，可能是格式化空白而不是有意义的内容
    const trimmedText = textContent.trim();
    if (trimmedText.length <= 5) {
      // 对于短文本，只有当包含特殊空格字符时才认为是有意义的
      if (/[\u00A0\u2000-\u200B\u2028\u2029]{3,}/.test(textContent)) {
        return true;
      }
      return false;
    }

    // 对于较长的文本，检查是否包含连续的特殊空格
    if (/[\u00A0\u2000-\u200B\u2028\u2029]{3,}/.test(textContent)) {
      return true;
    }

    return false;
  }

  /**
   * 检查文本是否为纯粹的格式化空白
   * 只有换行、制表符、普通空格的组合才被认为是格式化空白
   */
  private isPureFormattingWhitespace(text: string): boolean {
    // 如果文本为空，不是格式化空白
    if (!text) {
      return false;
    }

    // 如果包含非空白字符，不是纯格式化空白
    if (/[^\s]/.test(text)) {
      return false;
    }

    // 如果只包含普通空格、制表符、换行符，且长度较长（可能是缩进）
    if (/^[\s\t\n\r]+$/.test(text) && text.length > 2) {
      return true;
    }

    return false;
  }

  /**
   * 判断图片是否应该保持行内显示
   * 根据图片的上下文环境来决定
   */
  private shouldKeepImageInline(img: HTMLImageElement): boolean {
    // 检查图片是否在flex容器中（向上遍历DOM树）
    let currentElement: Element | null = img;

    while (currentElement) {
      // 检查当前元素是否是flex容器
      const style = currentElement.getAttribute('style') || '';
      const isFlexContainer = style.includes('display: flex') || style.includes('display:flex');

      if (isFlexContainer) {
        console.log('找到图片所在的flex容器');

        // 检查flex容器的直接子元素
        const flexChildren = Array.from(currentElement.children);

        // 检查flex容器是否包含题目序号（如"1）."、"2）."等）
        const hasQuestionNumber = flexChildren.some(child => {
          const text = child.textContent?.trim() || '';
          const isQuestionNumber = /^\d+[）)]\s*\.$/.test(text);
          if (isQuestionNumber) {
            console.log(`找到题目序号: "${text}"`);
          }
          return isQuestionNumber;
        });

        if (hasQuestionNumber) {
          console.log('图片在包含题目序号的flex容器中，保持行内显示');
          return true;
        } else {
          console.log('flex容器不包含题目序号，图片应该独立显示');
          break;
        }
      }

      // 向上遍历到父元素
      currentElement = currentElement.parentElement;
    }

    // 检查图片的直接父元素是否有特定的样式或类名
    const parent = img.parentElement;
    if (parent) {
      // 如果父元素是段落且包含其他内容，可能应该行内显示
      if (parent.tagName.toLowerCase() === 'p') {
        const siblings = Array.from(parent.children);
        const hasOtherContent = siblings.some(sibling =>
          sibling !== img && sibling.textContent?.trim()
        );

        if (hasOtherContent) {
          console.log('图片在包含其他内容的段落中，保持行内显示');
          return true;
        }
      }
    }

    // 默认情况下，图片应该独立显示
    console.log('图片不在特殊容器中，应该独立显示');
    return false;
  }

  /**
   * 判断flex容器的子元素是否应该内联处理
   * 更智能地处理包含图片或其他媒体内容的情况
   */
  private shouldInlineFlexChildren(children: Element[]): boolean {
    // 如果没有子元素，不需要内联
    if (children.length === 0) {
      return false;
    }

    // 检查是否包含题目序号模式（如"1）."、"2）."等）
    const hasQuestionNumber = children.some(child => {
      const text = child.textContent?.trim() || '';
      return /^\d+[）)]\s*\.$/.test(text);
    });

    if (hasQuestionNumber) {
      console.log('flex容器包含题目序号，应该内联处理');
      return true;
    }

    // 检查所有子元素的内容类型
    for (const child of children) {
      // 检查是否包含图片
      const hasImage = child.querySelector('img') !== null;

      if (hasImage) {
        // 如果包含图片，检查是否是简单的图片容器（如只包含一个图片的段落）
        const textWithoutImages = this.getTextContentWithoutImages(child);

        if (textWithoutImages.trim().length <= 15) {
          console.log('flex容器包含图片但文本内容较短，可以内联处理');
          continue; // 继续检查其他子元素
        } else {
          console.log(`flex容器包含图片和长文本内容，不进行内联处理: "${textWithoutImages.substring(0, 20)}..."`);
          return false;
        }
      } else {
        // 不包含图片的普通文本内容
        const textContent = child.textContent || '';
        const trimmedText = textContent.trim();

        // 如果任何一个子元素的文本内容超过15个字符，不应该内联
        if (trimmedText.length > 15) {
          console.log(`flex容器包含长文本内容，不进行内联处理: "${trimmedText.substring(0, 20)}..."`);
          return false;
        }

        // 检查是否包含特定的长内容标识
        if (
          trimmedText.includes('给加点字') ||
          trimmedText.includes('同学们向') ||
          trimmedText.includes('下列') ||
          trimmedText.includes('请根据') ||
          trimmedText.includes('形近字') ||
          trimmedText.includes('完成句子') ||
          trimmedText.includes('选择正确')
        ) {
          console.log(`flex容器包含题目内容，不进行内联处理: "${trimmedText.substring(0, 20)}..."`);
          return false;
        }
      }
    }

    // 如果所有子元素都是短文本或简单图片，可以内联
    console.log('flex容器包含短文本内容或简单图片，可以内联处理');
    return true;
  }

  /**
   * 获取元素的文本内容，排除图片等媒体元素
   */
  private getTextContentWithoutImages(element: Element): string {
    const clone = element.cloneNode(true) as Element;

    // 移除所有图片元素
    const images = clone.querySelectorAll('img');
    images.forEach(img => img.remove());

    // 移除其他媒体元素
    const media = clone.querySelectorAll('video, audio, canvas, svg');
    media.forEach(el => el.remove());

    return clone.textContent || '';
  }

  /**
   * 检查元素是否在flex容器内
   */
  private isElementInFlexContainer(element: HTMLElement): boolean {
    let parent = element.parentElement;

    while (parent) {
      if (parent.style.display === 'flex') {
        return true;
      }
      parent = parent.parentElement;
    }

    return false;
  }

  /**
   * 处理浮动元素
   * position:absolute等浮动元素在Word转换中可能造成问题
   * 对于flex容器内的绝对定位元素，转换为内联元素而不是简单移除
   */
  private removeFloatingElements(): void {
    console.log('处理浮动元素...');

    const floatingElements = this.document.querySelectorAll('*');
    let removedCount = 0;

    floatingElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      if (style.position === 'absolute' || style.position === 'fixed') {
        // 检查是否在flex容器内
        const isInFlexContainer = this.isElementInFlexContainer(htmlElement);

        if (isInFlexContainer) {
          // 检查元素内容，如果只包含br标签，则完全移除
          const textContent = htmlElement.textContent?.trim() || '';
          const innerHTML = htmlElement.innerHTML?.trim() || '';

          if (textContent === '' && (innerHTML === '<br>' || innerHTML === '<br/>')) {
            // 如果元素只包含br标签且没有其他文本内容，移除整个元素
            console.log(`移除flex容器内只包含br的绝对定位元素: ${htmlElement.tagName}`);
            htmlElement.remove();
          } else {
            // 对于有实际内容的flex容器内的绝对定位元素，转换为内联元素
            style.removeProperty('position');
            style.removeProperty('top');
            style.removeProperty('left');
            style.removeProperty('right');
            style.removeProperty('bottom');
            style.removeProperty('z-index');

            // 设置为内联块元素，保持在同一行
            style.display = 'inline-block';
            style.whiteSpace = 'nowrap';

            console.log(`flex容器内的绝对定位元素已转换为内联元素: ${htmlElement.tagName}`);
          }
        } else {
          // 对于非flex容器内的绝对定位元素，移除定位
          style.removeProperty('position');
          style.removeProperty('top');
          style.removeProperty('left');
          style.removeProperty('right');
          style.removeProperty('bottom');
        }

        removedCount++;
      }
    });

    if (removedCount > 0) {
      console.log(`处理了 ${removedCount} 个浮动定位样式`);
    }
  }

  /**
   * 优化表格样式
   * 彻底修复双线边框问题，确保单线边框
   */
  private optimizeTableStyles(): void {
    console.log('优化表格样式...');

    const tables = this.document.querySelectorAll('table');

    tables.forEach(table => {
      const htmlTable = table as HTMLTableElement;

      // 彻底清理表格样式，重新设置
      htmlTable.removeAttribute('border');
      htmlTable.removeAttribute('cellpadding');
      htmlTable.removeAttribute('cellspacing');

      // 关键设置：确保边框合并
      htmlTable.style.borderCollapse = 'collapse';
      htmlTable.style.borderSpacing = '0';

      // 移除表格本身的所有边框样式
      htmlTable.style.removeProperty('border');
      htmlTable.style.removeProperty('border-top');
      htmlTable.style.removeProperty('border-bottom');
      htmlTable.style.removeProperty('border-left');
      htmlTable.style.removeProperty('border-right');

      // 设置表格宽度
      if (!htmlTable.style.width) {
        htmlTable.style.width = '100%';
      }

      // 处理表格单元格，确保单线边框
      const cells = htmlTable.querySelectorAll('td, th');
      cells.forEach(cell => {
        const htmlCell = cell as HTMLTableCellElement;

        // 彻底清理单元格属性
        htmlCell.removeAttribute('width');
        htmlCell.removeAttribute('height');
        htmlCell.removeAttribute('border');
        htmlCell.removeAttribute('cellpadding');
        htmlCell.removeAttribute('cellspacing');

        // 清理所有边框样式
        htmlCell.style.removeProperty('border-top');
        htmlCell.style.removeProperty('border-bottom');
        htmlCell.style.removeProperty('border-left');
        htmlCell.style.removeProperty('border-right');
        htmlCell.style.removeProperty('border-width');
        htmlCell.style.removeProperty('border-style');
        htmlCell.style.removeProperty('border-color');

        // 重新设置单一边框 - 这是关键
        htmlCell.style.border = '1px solid black';

        // 设置内边距
        htmlCell.style.padding = '6px 8px';

        // 设置垂直对齐
        htmlCell.style.verticalAlign = 'top';

        // 确保文本换行正常
        htmlCell.style.overflowWrap = 'break-word';
      });
    });
  }

  /**
   * 清理可能导致问题的样式
   */
  private cleanupProblematicStyles(): void {
    console.log('清理问题样式...');

    const allElements = this.document.querySelectorAll('*');

    allElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      // 清理可能导致XML问题的样式属性
      const problematicProperties = [
        'white-space-collapse',
        'overflow-wrap',
        'word-break',
        'max-width',
      ];

      problematicProperties.forEach(prop => {
        if (style.getPropertyValue(prop)) {
          style.removeProperty(prop);
        }
      });

      // 清理可能有问题的类名
      if (htmlElement.className) {
        // 移除可能导致问题的类
        const problematicClasses = ['MsoNormal', 'double-underline'];
        problematicClasses.forEach(className => {
          htmlElement.classList.remove(className);
        });
      }

      // 清理align属性（使用style代替）
      if (htmlElement.hasAttribute('align')) {
        const alignValue = htmlElement.getAttribute('align');
        htmlElement.removeAttribute('align');
        if (alignValue && !style.textAlign) {
          style.textAlign = alignValue;
        }
      }

      // 特别处理表格单元格的width属性
      if (htmlElement.tagName === 'TD' || htmlElement.tagName === 'TH') {
        // 移除width属性，这可能导致XML问题
        htmlElement.removeAttribute('width');

        // 清理所有可能有问题的宽度样式
        if (style.width) {
          // 移除所有宽度样式，让表格自动调整
          style.removeProperty('width');
        }
      }

      // 特别处理表格的样式
      if (htmlElement.tagName === 'TABLE') {
        // 清理表格的高度和像素宽度
        if (style.height) {
          style.removeProperty('height');
        }
        if (style.width && style.width.includes('px')) {
          style.width = '100%';
        }
      }
    });
  }

  /**
   * 获取处理后的HTML
   */
  private getProcessedHtml(): string {
    return this.dom.serialize();
  }
}

/**
 * 预处理HTML内容
 * @param htmlContent 原始HTML内容
 * @param options 预处理选项
 * @returns 预处理后的HTML内容
 */
export function preprocessHtml(
  htmlContent: string,
  options: PreprocessOptions = {}
): string {
  const preprocessor = new HtmlPreprocessor(htmlContent);
  return preprocessor.preprocess(options);
}
