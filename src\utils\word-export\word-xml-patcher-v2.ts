/**
 * Word文档XML补丁处理器 V2
 * 简洁设计：自动检测并处理html-to-docx无法处理的特殊样式
 * 可扩展设计：易于添加新的特殊样式处理
 */
import * as JSZip from 'jszip';
import { DOMParser, XMLSerializer } from 'xmldom';

export interface EmphasisTarget {
  text: string;
  shouldHaveEmphasis: boolean;
}

export interface WavyTarget {
  text: string;
  shouldHaveWavy: boolean;
}

export interface UnderlineTarget {
  text: string;
  shouldHaveUnderline: boolean;
}

/**
 * Word文档XML补丁处理器
 * 职责：处理html-to-docx无法处理的特殊样式
 */
export class WordXmlPatcherV2 {
  private docxBuffer: Buffer;
  private originalHtml: string;

  constructor(docxBuffer: Buffer, originalHtml = '') {
    this.docxBuffer = docxBuffer;
    this.originalHtml = originalHtml;
  }

  /**
   * 应用补丁
   * 自动检测并处理html-to-docx无法处理的特殊样式
   */
  async applyPatches(): Promise<Buffer> {
    console.log('开始Word文档XML补丁处理...');

    // 自动检测需要处理的特殊样式
    const needsProcessing = this.detectSpecialStyles();

    if (!needsProcessing) {
      console.log('无需XML补丁处理，返回原始文档');
      return this.docxBuffer;
    }

    try {
      // 解析Word文档
      const zip = new JSZip();
      const docxZip = await zip.loadAsync(this.docxBuffer);

      // 读取document.xml
      const documentXml = await docxZip
        .file('word/document.xml')
        ?.async('text');
      if (!documentXml) {
        throw new Error('无法读取Word文档内容');
      }

      console.log('成功读取Word文档XML');

      // 解析XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(documentXml, 'text/xml');

      let modified = false;

      // 处理着重号（如果检测到）
      if (this.hasEmphasisMarks()) {
        const emphasisProcessed = await this.processEmphasisMarks(xmlDoc);
        if (emphasisProcessed) {
          modified = true;
          console.log('着重号处理完成');
        }
      }

      // 处理下横线（如果检测到）
      if (this.hasUnderlineMarks()) {
        const underlineProcessed = await this.processUnderlineMarks(xmlDoc);
        if (underlineProcessed) {
          modified = true;
          console.log('下横线处理完成');
        }
      }

      // 处理波浪线（如果检测到）
      if (this.hasWavyUnderlines()) {
        const wavyProcessed = await this.processWavyUnderlines(xmlDoc);
        if (wavyProcessed) {
          modified = true;
          console.log('波浪线处理完成');
        }
      }

      // 扩展点：在这里添加其他特殊样式处理
      // 处理不必要的换行（如果检测到）
      if (this.hasUnnecessaryLineBreaks()) {
        const lineBreaksProcessed = await this.processUnnecessaryLineBreaks(
          xmlDoc
        );
        if (lineBreaksProcessed) {
          modified = true;
          console.log('换行优化处理完成');
        }
      }

      // 清理html-to-docx产生的多余空格
      const excessiveSpacesProcessed = await this.cleanExcessiveSpaces(xmlDoc);
      if (excessiveSpacesProcessed) {
        modified = true;
        console.log('多余空格清理完成');
      }

      // 处理下横线效果（暂时禁用，检查html-to-docx原始效果）
      // const underlineProcessed = await this.processUnderlineSpaces(xmlDoc);
      // if (underlineProcessed) {
      //   modified = true;
      //   console.log('下横线效果处理完成');
      // }

      // 处理文档格式优化（行间距和对齐）
      if (this.needsDocumentFormatting()) {
        const formattingProcessed = await this.processDocumentFormatting(
          xmlDoc
        );
        if (formattingProcessed) {
          modified = true;
          console.log('文档格式优化处理完成');
        }
      }

      // 处理表格边框优化
      if (this.needsTableFormatting()) {
        const tableProcessed = await this.processTableFormatting(xmlDoc);
        if (tableProcessed) {
          modified = true;
          console.log('表格边框优化处理完成');
        }
      }

      // 处理图片（如果检测到）
      if (this.hasImages()) {
        const imagesProcessed = await this.processImages(xmlDoc, docxZip);
        if (imagesProcessed) {
          modified = true;
          console.log('图片处理完成');
        }
      }

      // if (this.hasSpecialFonts()) {
      //   const fontsProcessed = await this.processSpecialFonts(xmlDoc);
      //   if (fontsProcessed) modified = true;
      // }
      // if (this.hasSpecialColors()) {
      //   const colorsProcessed = await this.processSpecialColors(xmlDoc);
      //   if (colorsProcessed) modified = true;
      // }

      if (modified) {
        // 序列化修改后的XML
        const serializer = new XMLSerializer();
        const modifiedXml = serializer.serializeToString(xmlDoc);

        // 更新ZIP中的document.xml
        docxZip.file('word/document.xml', modifiedXml);

        // 生成新的Word文档
        const patchedBuffer = await docxZip.generateAsync({
          type: 'nodebuffer',
        });

        console.log('Word文档XML补丁处理完成');
        return patchedBuffer;
      } else {
        console.log('无需修改，返回原始文档');
        return this.docxBuffer;
      }
    } catch (error) {
      console.error('XML补丁处理失败，回退到原始文档:', error);
      return this.docxBuffer;
    }
  }

  /**
   * 检测需要特殊处理的样式
   */
  private detectSpecialStyles(): boolean {
    const hasEmphasis = this.hasEmphasisMarks();
    const hasWavy = this.hasWavyUnderlines();
    const hasLineBreaks = this.hasUnnecessaryLineBreaks();
    const needsFormatting = this.needsDocumentFormatting();
    const needsTableFormatting = this.needsTableFormatting();

    if (hasEmphasis) {
      console.log('检测到着重号，需要XML补丁处理');
      return true;
    }

    if (hasWavy) {
      console.log('检测到波浪线，需要XML补丁处理');
      return true;
    }

    if (hasLineBreaks) {
      console.log('检测到不必要的换行，需要XML补丁处理');
      return true;
    }

    if (needsFormatting) {
      console.log('检测到需要文档格式优化，需要XML补丁处理');
      return true;
    }

    if (needsTableFormatting) {
      console.log('检测到表格，需要边框样式优化');
      return true;
    }

    const hasImages = this.hasImages();
    if (hasImages) {
      console.log('检测到图片，需要XML补丁处理');
      return true;
    }

    // 扩展点：添加其他特殊样式检测
    // if (hasSpecialFonts) {
    //   console.log('检测到特殊字体，需要XML补丁处理');
    //   return true;
    // }

    return false;
  }

  /**
   * 检测是否有着重号需要处理
   */
  private hasEmphasisMarks(): boolean {
    // 检查HTML中是否包含着重号的特殊标记
    const hasEmphasisMarks = /\[EMPHASIS\].*?\[\/EMPHASIS\]/g.test(
      this.originalHtml
    );

    if (hasEmphasisMarks) {
      console.log(
        '从HTML中提取了',
        (this.originalHtml.match(/\[EMPHASIS\].*?\[\/EMPHASIS\]/g) || [])
          .length,
        '个着重号目标（特殊标记）'
      );
    }

    return hasEmphasisMarks;
  }

  /**
   * 检测是否有波浪线需要处理
   */
  private hasWavyUnderlines(): boolean {
    // 检查HTML中是否包含波浪线的特殊标记
    const hasWavyMarks = /\[WAVY\].*?\[\/WAVY\]/g.test(this.originalHtml);

    if (hasWavyMarks) {
      console.log(
        '从HTML中提取了',
        (this.originalHtml.match(/\[WAVY\].*?\[\/WAVY\]/g) || []).length,
        '个波浪线目标（特殊标记）'
      );
    }

    return hasWavyMarks;
  }

  /**
   * 检测是否有下横线需要处理
   */
  private hasUnderlineMarks(): boolean {
    // 检查HTML中是否包含下横线的特殊标记
    const hasUnderlineMarks = /\[UNDERLINE\].*?\[\/UNDERLINE\]/g.test(this.originalHtml);

    if (hasUnderlineMarks) {
      console.log(
        '从HTML中提取了',
        (this.originalHtml.match(/\[UNDERLINE\].*?\[\/UNDERLINE\]/g) || []).length,
        '个下横线目标（特殊标记）'
      );
    }

    return hasUnderlineMarks;
  }

  /**
   * 处理着重号
   */
  private async processEmphasisMarks(xmlDoc: Document): Promise<boolean> {
    try {
      // 从HTML中提取需要着重号的文本
      const emphasisTargets = this.extractEmphasisTargets();

      if (emphasisTargets.length === 0) {
        console.log('未找到需要处理的着重号');
        return false;
      }

      console.log(`找到 ${emphasisTargets.length} 个需要着重号的文本`);

      // 在Word XML中查找并修改对应的文本节点
      let patchCount = 0;

      for (const target of emphasisTargets) {
        if (this.patchTextNodeEmphasis(xmlDoc, target.text)) {
          patchCount++;
        }
      }

      console.log(`成功应用 ${patchCount} 个着重号补丁`);
      return patchCount > 0;
    } catch (error) {
      console.error('着重号处理失败:', error);
      return false;
    }
  }

  /**
   * 从HTML中提取需要着重号的文本
   */
  private extractEmphasisTargets(): EmphasisTarget[] {
    const targets: EmphasisTarget[] = [];

    // 使用正则表达式提取所有着重号标记
    const emphasisPattern = /\[EMPHASIS\](.*?)\[\/EMPHASIS\]/g;
    let match: RegExpExecArray | null;

    while ((match = emphasisPattern.exec(this.originalHtml)) !== null) {
      const text = match[1];
      if (text && text.trim()) {
        targets.push({
          text: text.trim(),
          shouldHaveEmphasis: true,
        });
      }
    }

    return targets;
  }

  /**
   * 处理波浪线
   */
  private async processWavyUnderlines(xmlDoc: Document): Promise<boolean> {
    try {
      // 从HTML中提取需要波浪线的文本
      const wavyTargets = this.extractWavyTargets();

      if (wavyTargets.length === 0) {
        console.log('未找到需要处理的波浪线');
        return false;
      }

      console.log(`找到 ${wavyTargets.length} 个需要波浪线的文本`);

      // 在Word XML中查找并修改对应的文本节点
      let patchCount = 0;

      for (const target of wavyTargets) {
        if (this.patchTextNodeWavy(xmlDoc, target.text)) {
          patchCount++;
        }
      }

      console.log(`成功应用 ${patchCount} 个波浪线补丁`);
      return patchCount > 0;
    } catch (error) {
      console.error('波浪线处理失败:', error);
      return false;
    }
  }

  /**
   * 从HTML中提取需要波浪线的文本
   */
  private extractWavyTargets(): WavyTarget[] {
    const targets: WavyTarget[] = [];

    // 使用正则表达式提取所有波浪线标记
    const wavyPattern = /\[WAVY\](.*?)\[\/WAVY\]/g;
    let match: RegExpExecArray | null;

    while ((match = wavyPattern.exec(this.originalHtml)) !== null) {
      const text = match[1];
      if (text && text.trim()) {
        targets.push({
          text: text.trim(),
          shouldHaveWavy: true,
        });
      }
    }

    return targets;
  }

  /**
   * 处理下横线
   */
  private async processUnderlineMarks(xmlDoc: Document): Promise<boolean> {
    try {
      // 从HTML中提取需要下横线的文本
      const underlineTargets = this.extractUnderlineTargets();

      if (underlineTargets.length === 0) {
        console.log('未找到需要处理的下横线');
        return false;
      }

      console.log(`找到 ${underlineTargets.length} 个需要下横线的文本`);

      // 直接查找所有包含下横线标记的文本节点并处理
      let patchCount = 0;
      const textNodes = xmlDoc.getElementsByTagName('w:t');

      for (let i = 0; i < textNodes.length; i++) {
        const textNode = textNodes[i];
        const textContent = textNode.textContent || '';

        // 检查是否包含任何下横线标记
        if (/\[UNDERLINE\].*?\[\/UNDERLINE\]/.test(textContent)) {
          console.log(`发现包含下横线标记的文本节点: ${textContent.substring(0, 50)}...`);

          // 找到包含此文本节点的运行(w:r)和段落(w:p)
          const runNode = this.findParentRun(textNode);
          const paragraphNode = this.findParentParagraph(runNode);

          if (runNode && paragraphNode) {
            // 处理包含下横线标记的文本
            const processed = this.processAllUnderlineInText(
              xmlDoc,
              textContent,
              paragraphNode,
              runNode
            );
            if (processed) {
              console.log(`成功处理包含下横线标记的文本节点`);
              patchCount++;
              // 重新开始遍历，因为DOM结构已改变
              i = -1;
            }
          }
        }
      }

      console.log(`成功应用 ${patchCount} 个下横线补丁`);
      return patchCount > 0;
    } catch (error) {
      console.error('下横线处理失败:', error);
      return false;
    }
  }

  /**
   * 从HTML中提取需要下横线的文本
   */
  private extractUnderlineTargets(): UnderlineTarget[] {
    const targets: UnderlineTarget[] = [];

    // 使用正则表达式提取所有下横线标记
    const underlinePattern = /\[UNDERLINE\](.*?)\[\/UNDERLINE\]/g;
    let match: RegExpExecArray | null;

    while ((match = underlinePattern.exec(this.originalHtml)) !== null) {
      const text = match[1];
      if (text) {
        // 不要trim，保留原始的空格内容（包括nbsp）
        targets.push({
          text: text,
          shouldHaveUnderline: true,
        });
      }
    }

    return targets;
  }

  /**
   * 处理包含所有下横线标记的文本节点
   */
  private processAllUnderlineInText(
    xmlDoc: Document,
    fullText: string,
    paragraphNode: Element,
    originalRunNode: Element
  ): boolean {
    try {
      // 查找所有下横线标记
      const underlinePattern = /\[UNDERLINE\](.*?)\[\/UNDERLINE\]/g;
      const matches: Array<{fullMatch: string, content: string, index: number}> = [];
      let match: RegExpExecArray | null;

      while ((match = underlinePattern.exec(fullText)) !== null) {
        matches.push({
          fullMatch: match[0],
          content: match[1],
          index: match.index
        });
      }

      if (matches.length === 0) {
        return false;
      }

      console.log(`在文本中找到 ${matches.length} 个下横线标记`);

      // 获取原始运行的样式
      const originalRPr = originalRunNode.getElementsByTagName('w:rPr')[0];

      // 分割文本并重建运行节点
      let currentPosition = 0;

      // 按顺序处理每个标记
      for (const matchInfo of matches) {
        const { fullMatch, content, index } = matchInfo;

        // 添加标记前的普通文本
        if (index > currentPosition) {
          const beforeText = fullText.substring(currentPosition, index);
          if (beforeText) {
            const normalRun = this.createTextRun(xmlDoc, beforeText, 'normal', originalRPr);
            paragraphNode.insertBefore(normalRun, originalRunNode);
          }
        }

        // 添加下横线文本
        const underlineRun = this.createTextRun(xmlDoc, content, 'underline', originalRPr);
        paragraphNode.insertBefore(underlineRun, originalRunNode);

        // 更新当前位置
        currentPosition = index + fullMatch.length;
      }

      // 添加最后剩余的普通文本
      if (currentPosition < fullText.length) {
        const afterText = fullText.substring(currentPosition);
        if (afterText) {
          const normalRun = this.createTextRun(xmlDoc, afterText, 'normal', originalRPr);
          paragraphNode.insertBefore(normalRun, originalRunNode);
        }
      }

      // 移除原始运行节点
      paragraphNode.removeChild(originalRunNode);

      return true;
    } catch (error) {
      console.error('处理所有下横线标记失败:', error);
      return false;
    }
  }





  /**
   * 在Word XML中查找并修改文本节点，添加着重号
   */
  private patchTextNodeEmphasis(xmlDoc: Document, targetText: string): boolean {
    try {
      // 查找所有文本节点
      const textNodes = xmlDoc.getElementsByTagName('w:t');

      for (let i = 0; i < textNodes.length; i++) {
        const textNode = textNodes[i];
        const textContent = textNode.textContent || '';

        // 检查是否包含特殊标记
        const emphasisPattern = `[EMPHASIS]${targetText}[/EMPHASIS]`;
        if (textContent.includes(emphasisPattern)) {
          console.log(`找到特殊标记文本: "${emphasisPattern}"`);

          // 找到包含此文本节点的运行(w:r)和段落(w:p)
          const runNode = this.findParentRun(textNode);
          const paragraphNode = this.findParentParagraph(runNode);

          if (runNode && paragraphNode) {
            // 处理包含特殊标记的文本
            const processed = this.processEmphasisInText(
              xmlDoc,
              textContent,
              targetText,
              paragraphNode,
              runNode
            );
            if (processed) {
              console.log(`成功处理特殊标记: "${targetText}"`);
              return true;
            }
          }
        }
      }

      return false;
    } catch (error) {
      console.error(`处理着重号文本失败: ${targetText}`, error);
      return false;
    }
  }

  /**
   * 在Word XML中查找并修改文本节点，添加波浪线
   */
  private patchTextNodeWavy(xmlDoc: Document, targetText: string): boolean {
    try {
      // 查找所有文本节点
      const textNodes = xmlDoc.getElementsByTagName('w:t');

      for (let i = 0; i < textNodes.length; i++) {
        const textNode = textNodes[i];
        const textContent = textNode.textContent || '';

        // 检查是否包含特殊标记
        const wavyPattern = `[WAVY]${targetText}[/WAVY]`;
        if (textContent.includes(wavyPattern)) {
          console.log(`找到波浪线特殊标记文本: "${wavyPattern}"`);

          // 找到包含此文本节点的运行(w:r)和段落(w:p)
          const runNode = this.findParentRun(textNode);
          const paragraphNode = this.findParentParagraph(runNode);

          if (runNode && paragraphNode) {
            // 处理包含特殊标记的文本
            const processed = this.processWavyInText(
              xmlDoc,
              textContent,
              targetText,
              paragraphNode,
              runNode
            );
            if (processed) {
              console.log(`成功处理波浪线特殊标记: "${targetText}"`);
              return true;
            }
          }
        }
      }

      return false;
    } catch (error) {
      console.error(`处理波浪线文本失败: ${targetText}`, error);
      return false;
    }
  }



  /**
   * 处理包含特殊标记的文本，将其分割并添加着重号
   */
  private processEmphasisInText(
    xmlDoc: Document,
    fullText: string,
    targetText: string,
    paragraphNode: Element,
    originalRunNode: Element
  ): boolean {
    try {
      // 构建特殊标记模式
      const emphasisPattern = `[EMPHASIS]${targetText}[/EMPHASIS]`;

      // 分割文本
      const parts = fullText.split(emphasisPattern);
      const matches =
        fullText.match(
          new RegExp(
            emphasisPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
            'g'
          )
        ) || [];

      if (matches.length === 0) {
        return false;
      }

      // 获取原始运行的样式（但不包括可能的意外样式）
      const originalRPr = originalRunNode.getElementsByTagName('w:rPr')[0];

      // 创建新的运行节点来替换原始节点
      let partIndex = 0;
      for (let i = 0; i < parts.length; i++) {
        // 添加普通文本部分
        if (parts[i]) {
          const normalRun = this.createTextRun(
            xmlDoc,
            parts[i],
            'normal',
            originalRPr
          );
          paragraphNode.insertBefore(normalRun, originalRunNode);
        }

        // 添加着重号文本部分
        if (partIndex < matches.length) {
          const emphasisRun = this.createTextRun(
            xmlDoc,
            targetText,
            'emphasis',
            originalRPr
          );
          paragraphNode.insertBefore(emphasisRun, originalRunNode);
          partIndex++;
        }
      }

      // 移除原始运行节点
      paragraphNode.removeChild(originalRunNode);

      return true;
    } catch (error) {
      console.error('处理特殊标记文本失败:', error);
      return false;
    }
  }

  /**
   * 处理包含波浪线特殊标记的文本，将其分割并添加波浪线
   */
  private processWavyInText(
    xmlDoc: Document,
    fullText: string,
    targetText: string,
    paragraphNode: Element,
    originalRunNode: Element
  ): boolean {
    try {
      // 构建特殊标记模式
      const wavyPattern = `[WAVY]${targetText}[/WAVY]`;

      // 分割文本
      const parts = fullText.split(wavyPattern);
      const matches =
        fullText.match(
          new RegExp(wavyPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
        ) || [];

      if (matches.length === 0) {
        return false;
      }

      // 获取原始运行的样式
      const originalRPr = originalRunNode.getElementsByTagName('w:rPr')[0];

      // 创建新的运行节点来替换原始节点
      let partIndex = 0;
      for (let i = 0; i < parts.length; i++) {
        // 添加普通文本部分
        if (parts[i]) {
          const normalRun = this.createTextRun(
            xmlDoc,
            parts[i],
            'normal',
            originalRPr
          );
          paragraphNode.insertBefore(normalRun, originalRunNode);
        }

        // 添加波浪线文本部分
        if (partIndex < matches.length) {
          const wavyRun = this.createTextRun(
            xmlDoc,
            targetText,
            'wavy',
            originalRPr
          );
          paragraphNode.insertBefore(wavyRun, originalRunNode);
          partIndex++;
        }
      }

      // 移除原始运行节点
      paragraphNode.removeChild(originalRunNode);

      return true;
    } catch (error) {
      console.error('处理波浪线特殊标记文本失败:', error);
      return false;
    }
  }





  /**
   * 创建文本运行节点
   */
  private createTextRun(
    xmlDoc: Document,
    text: string,
    styleType: 'normal' | 'emphasis' | 'wavy' | 'underline',
    originalRPr?: Element | null
  ): Element {
    const run = xmlDoc.createElement('w:r');

    // 创建运行属性
    const rPrNode = xmlDoc.createElement('w:rPr');

    // 首先复制原有的样式属性（保留字体、颜色、大小等）
    if (originalRPr) {
      // 复制原有运行属性的所有子节点，但排除可能冲突的样式
      const childNodes = Array.from(originalRPr.childNodes);
      for (const childNode of childNodes) {
        const nodeName = childNode.nodeName;

        // 跳过可能与新样式冲突的属性
        if (styleType === 'emphasis' && nodeName === 'w:em') {
          continue; // 跳过原有的着重号设置
        }
        if ((styleType === 'wavy' || styleType === 'underline') && nodeName === 'w:u') {
          continue; // 跳过原有的下划线设置
        }

        // 复制其他样式属性（字体、颜色、大小等）
        rPrNode.appendChild(childNode.cloneNode(true));
      }
    }

    // 根据样式类型添加相应的特殊样式
    if (styleType === 'emphasis') {
      // 添加着重号样式
      const emphasisNode = xmlDoc.createElement('w:em');
      emphasisNode.setAttribute('w:val', 'dot');
      rPrNode.appendChild(emphasisNode);
    } else if (styleType === 'wavy') {
      // 添加波浪线下划线样式
      const underlineNode = xmlDoc.createElement('w:u');
      underlineNode.setAttribute('w:val', 'wave');
      rPrNode.appendChild(underlineNode);
    } else if (styleType === 'underline') {
      // 添加普通下划线样式
      const underlineNode = xmlDoc.createElement('w:u');
      underlineNode.setAttribute('w:val', 'single');
      rPrNode.appendChild(underlineNode);
    }
    // 对于 'normal' 类型，只保留原有样式，不添加特殊样式

    run.appendChild(rPrNode);

    // 创建文本节点
    const textNode = xmlDoc.createElement('w:t');
    textNode.textContent = text;
    run.appendChild(textNode);

    return run;
  }

  /**
   * 查找文本节点的父运行节点
   */
  private findParentRun(textNode: Node): Element | null {
    let parent = textNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:r') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  /**
   * 查找运行节点的父段落节点
   */
  private findParentParagraph(runNode: Element | null): Element | null {
    if (!runNode) return null;

    let parent = runNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:p') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  /**
   * 检测是否有不必要的换行需要处理
   */
  private hasUnnecessaryLineBreaks(): boolean {
    // 检查HTML中是否包含inline-block元素
    const hasInlineBlock = /display:\s*inline-block/g.test(this.originalHtml);

    // 检查是否包含flex容器
    const hasFlexContainer = /display:\s*flex/g.test(this.originalHtml);

    // 检查是否包含选项标签模式
    const hasOptionPattern = /[A-Z][、.]\s*/.test(this.originalHtml);

    // 检查是否包含题目序号模式
    const hasNumberPattern = /\d+[、.]\s*/.test(this.originalHtml);

    const hasInlineElements =
      hasInlineBlock ||
      hasFlexContainer ||
      hasOptionPattern ||
      hasNumberPattern;

    if (hasInlineElements) {
      console.log('检测到内联元素，可能需要换行优化', {
        hasInlineBlock,
        hasFlexContainer,
        hasOptionPattern,
        hasNumberPattern,
      });
    }

    return hasInlineElements;
  }

  /**
   * 处理不必要的换行
   * 基于inline/inline-block元素的通用处理方案
   */
  private async processUnnecessaryLineBreaks(
    xmlDoc: Document
  ): Promise<boolean> {
    try {
      console.log('开始处理不必要的换行...');

      // 查找所有段落
      const paragraphs = xmlDoc.getElementsByTagName('w:p');
      let processedCount = 0;

      // 遍历段落，查找需要合并的相邻段落
      for (let i = 0; i < paragraphs.length - 1; i++) {
        const currentParagraph = paragraphs[i];
        const nextParagraph = paragraphs[i + 1];

        const currentText = this.getParagraphText(currentParagraph);
        const nextText = this.getParagraphText(nextParagraph);

        // 1. 处理选项标签和内容对
        if (this.isOptionLabelAndContentPair(currentText, nextText)) {
          console.log(
            `合并选项标签和内容: "${currentText}" + "${nextText.substring(
              0,
              20
            )}..."`
          );
          this.mergeParagraphs(xmlDoc, currentParagraph, nextParagraph);
          // 合并后清理段落缩进
          this.cleanParagraphIndentation(xmlDoc, currentParagraph);
          processedCount++;
          i = -1; // 重新开始遍历
        }
        // 2. 处理题目序号和内容对
        else if (this.isQuestionNumberAndContentPair(currentText, nextText)) {
          console.log(
            `合并题目序号和内容: "${currentText}" + "${nextText.substring(
              0,
              20
            )}..."`
          );
          this.mergeParagraphs(xmlDoc, currentParagraph, nextParagraph);
          // 合并后清理段落缩进
          this.cleanParagraphIndentation(xmlDoc, currentParagraph);
          processedCount++;
          i = -1;
        }
        // 3. 处理flex容器中的内联元素（通用处理）
        else if (this.shouldMergeFlexElements(currentText, nextText)) {
          console.log(
            `合并flex容器元素: "${currentText}" + "${nextText.substring(
              0,
              20
            )}..."`
          );
          this.mergeParagraphs(xmlDoc, currentParagraph, nextParagraph);
          // 合并后清理段落缩进
          this.cleanParagraphIndentation(xmlDoc, currentParagraph);
          processedCount++;
          i = -1;
        }
      }

      console.log(`成功合并 ${processedCount} 个内联段落`);
      return processedCount > 0;
    } catch (error) {
      console.error('换行处理失败:', error);
      return false;
    }
  }

  /**
   * 清理html-to-docx产生的多余空格
   * 专门处理flex容器元素中的格式化空白
   */
  private async cleanExcessiveSpaces(xmlDoc: Document): Promise<boolean> {
    try {
      console.log('开始清理多余空格...');

      const paragraphs = xmlDoc.getElementsByTagName('w:p');
      let cleanedCount = 0;

      for (let i = 0; i < paragraphs.length; i++) {
        const paragraph = paragraphs[i];
        const runs = paragraph.getElementsByTagName('w:r');

        for (let j = 0; j < runs.length; j++) {
          const run = runs[j];
          const textNodes = run.getElementsByTagName('w:t');

          for (let k = 0; k < textNodes.length; k++) {
            const textNode = textNodes[k];
            const originalText = textNode.textContent || '';

            // 检查是否是纯格式化空白（大量连续的普通空格）
            if (this.isFormattingWhitespace(originalText)) {
              // 清理格式化空白，但保留有意义的内容
              const cleanedText = this.cleanFormattingWhitespace(originalText);

              if (cleanedText !== originalText) {
                textNode.textContent = cleanedText;
                cleanedCount++;
              }
            }
          }
        }
      }

      console.log(`清理了 ${cleanedCount} 个多余空格节点`);
      return cleanedCount > 0;
    } catch (error) {
      console.error('清理多余空格失败:', error);
      return false;
    }
  }

  /**
   * 检查文本是否为格式化空白
   */
  private isFormattingWhitespace(text: string): boolean {
    // 如果文本为空，不是格式化空白
    if (!text) return false;

    // 如果包含非空白字符，检查是否是带有大量前后空格的短文本
    const trimmedText = text.trim();
    if (trimmedText.length > 0 && trimmedText.length <= 5) {
      // 短文本（如"一、"、"1."）但前后有大量空格
      const totalSpaces = text.length - trimmedText.length;
      if (totalSpaces >= 10) {
        return true; // 认为是格式化空白
      }
    }

    // 如果是纯空格且数量较多，可能是格式化空白
    if (/^\s+$/.test(text) && text.length >= 10) {
      // 但要排除包含非断行空格的情况（可能是有意义的下横线）
      if (!/[\u00A0\u2000-\u200B\u2028\u2029]/.test(text)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 清理格式化空白
   */
  private cleanFormattingWhitespace(text: string): string {
    const trimmedText = text.trim();

    // 如果有实际内容，保留内容但移除多余空格
    if (trimmedText.length > 0) {
      return trimmedText;
    }

    // 如果是纯空格，保留一个空格（避免完全删除可能有用的间距）
    return ' ';
  }



  /**
   * 检查是否需要文档格式优化
   * 包括行间距和文本对齐等
   */
  private needsDocumentFormatting(): boolean {
    // 检查是否包含居中对齐的内容
    const hasCenterAlignment = /text-align:\s*center/g.test(this.originalHtml);

    // 检查是否包含行间距设置
    const hasLineHeight = /line-height:\s*1\.5/g.test(this.originalHtml);

    // 如果有居中对齐或行间距设置，需要格式优化
    const needsFormatting = hasCenterAlignment || hasLineHeight;

    if (needsFormatting) {
      console.log('检测到需要格式优化的内容：', {
        hasCenterAlignment,
        hasLineHeight,
      });
    }

    return needsFormatting;
  }

  /**
   * 合并两个段落
   */
  private mergeParagraphs(
    xmlDoc: Document,
    currentParagraph: Element,
    nextParagraph: Element
  ): void {
    // 将下一个段落的所有运行节点移动到当前段落
    const nextRuns = nextParagraph.getElementsByTagName('w:r');

    // 获取当前段落和下一段落的文本内容来决定间距
    const currentText = this.getParagraphText(currentParagraph).trim();
    const nextText = this.getParagraphText(nextParagraph).trim();

    // 根据内容类型决定间距
    let spacingText = ''; // 默认不添加空格

    // 对于flex容器元素，不添加额外空格
    if (this.shouldMergeFlexElements(currentText, nextText)) {
      spacingText = this.getFlexSpacing();
    }
    // 对于选项标签，使用单个空格
    else if (this.isOptionLabelAndContentPair(currentText, nextText)) {
      spacingText = ' ';
    }
    // 对于题目序号，使用单个空格
    else if (this.isQuestionNumberAndContentPair(currentText, nextText)) {
      spacingText = ' ';
    }

    // 只有当需要添加空格时才创建空格节点
    if (spacingText) {
      const spaceRun = this.createSimpleTextRun(xmlDoc, spacingText);
      currentParagraph.appendChild(spaceRun);
    }

    // 复制所有运行节点
    const runsToMove = Array.from(nextRuns);
    for (const run of runsToMove) {
      currentParagraph.appendChild(run.cloneNode(true));
    }

    // 删除下一个段落
    const parent = nextParagraph.parentNode;
    if (parent) {
      parent.removeChild(nextParagraph);
    }
  }

  /**
   * 获取flex容器的间距
   * 根据HTML中的gap属性来确定合适的间距
   */
  private getFlexSpacing(): string {
    // 检查HTML中是否有gap属性
    const gapMatch = this.originalHtml.match(/gap:\s*(\d+)px/);

    if (gapMatch) {
      const gapValue = parseInt(gapMatch[1], 10);

      // 对于flex容器的gap，使用极其保守的间距
      // Word中的空格显示效果比HTML中大很多
      if (gapValue >= 20) {
        // 对于20px或更大的gap，使用2个空格
        return '  '; // 2个空格
      } else if (gapValue >= 10) {
        // 对于10-19px的gap，使用1个空格
        return ' '; // 1个空格
      } else {
        // 对于小于10px的gap（包括8px），不添加额外空格
        // 让Word的默认字符间距来处理
        return '';
      }
    }

    // 默认不添加额外空格
    return '';
  }

  /**
   * 清理段落缩进
   * 移除可能导致过多空白的缩进设置
   */
  private cleanParagraphIndentation(xmlDoc: Document, paragraph: Element): void {
    try {
      // 获取段落属性
      const pPr = paragraph.getElementsByTagName('w:pPr')[0];
      if (!pPr) return;

      // 检查并移除可能的缩进设置
      const ind = pPr.getElementsByTagName('w:ind')[0];
      if (ind) {
        // 检查是否有过大的缩进值
        const leftIndent = ind.getAttribute('w:left');
        const firstLineIndent = ind.getAttribute('w:firstLine');

        if (leftIndent && parseInt(leftIndent) > 720) { // 720 twips = 0.5 inch
          ind.setAttribute('w:left', '0');
        }

        if (firstLineIndent && parseInt(firstLineIndent) > 360) { // 360 twips = 0.25 inch
          ind.setAttribute('w:firstLine', '0');
        }

        // 如果所有缩进都为0，移除整个缩进元素
        const left = ind.getAttribute('w:left') || '0';
        const firstLine = ind.getAttribute('w:firstLine') || '0';
        const hanging = ind.getAttribute('w:hanging') || '0';

        if (left === '0' && firstLine === '0' && hanging === '0') {
          pPr.removeChild(ind);
        }
      }
    } catch (error) {
      console.warn('清理段落缩进失败:', error);
    }
  }

  /**
   * 创建简单的文本运行节点
   */
  private createSimpleTextRun(xmlDoc: Document, text: string): Element {
    const run = xmlDoc.createElement('w:r');
    const textNode = xmlDoc.createElement('w:t');
    textNode.textContent = text;
    run.appendChild(textNode);
    return run;
  }

  /**
   * 获取段落的文本内容
   */
  private getParagraphText(paragraph: Element): string {
    const textNodes = paragraph.getElementsByTagName('w:t');
    let text = '';
    for (let i = 0; i < textNodes.length; i++) {
      text += textNodes[i].textContent || '';
    }
    return text.trim();
  }

  /**
   * 检测选项标签和内容对
   */
  private isOptionLabelAndContentPair(text1: string, text2: string): boolean {
    // 第一个是纯选项标签：只有" A、 ", " B、 ", " C、 ", " D、 " 等，没有其他内容
    const isLabel = /^\s*[A-Z][、.]\s*$/.test(text1);

    // 第二个是选项内容（不为空且不是另一个选项标签，也不是完整的选项）
    const isContent =
      text2.length > 0 &&
      !/^\s*[A-Z][、.]\s*$/.test(text2) &&
      !/^\s*[A-Z][、.]\s+/.test(text2); // 不是以"A、 内容"开头的完整选项

    return isLabel && isContent;
  }

  /**
   * 检测题目序号和内容对
   */
  private isQuestionNumberAndContentPair(
    text1: string,
    text2: string
  ): boolean {
    // 第一个是题目序号：" 1. ", " 2、 " 等
    const isNumber = /^\s*\d+[、.]\s*$/.test(text1);

    // 第二个是题目内容（不为空且不是另一个序号）
    const isContent = text2.length > 0 && !/^\s*\d+[、.]\s*$/.test(text2);

    return isNumber && isContent;
  }

  /**
   * 检测是否应该合并flex容器中的元素
   * 更保守的策略：只合并确实应该在同一行的flex子元素
   * 避免合并flex容器本身与相邻元素
   */
  private shouldMergeFlexElements(text1: string, text2: string): boolean {
    // 只有当HTML中包含flex容器时才进行此类合并
    const hasFlexContainer = /display:\s*flex/g.test(this.originalHtml);
    if (!hasFlexContainer) {
      return false;
    }

    // 检查是否不是已经处理过的特殊模式（避免重复处理）
    const isNotOptionPattern =
      !/^\s*[A-Z][、.]\s*$/.test(text1) && !/^\s*[A-Z][、.]\s+/.test(text2);
    const isNotNumberPattern =
      !/^\s*\d+[、.]\s*$/.test(text1) && !/^\s*\d+[、.]\s+/.test(text2);

    // 如果已经是选项或题目序号模式，不要重复处理
    if (!isNotOptionPattern || !isNotNumberPattern) {
      return false;
    }

    // 更严格的flex内容特征检测
    // 只匹配确实应该在同一行的内容组合
    const isFlexInlinePattern =
      // 1. 日期和标题的组合（头部信息）
      (/\d{4}年\d{1,2}月\d{1,2}日/.test(text1) &&
        /姓名|班级|考号/.test(text2)) ||
      (/姓名|班级|考号/.test(text1) &&
        /\d{4}年\d{1,2}月\d{1,2}日/.test(text2)) ||
      // 2. 大题序号和标题的组合（如"一、" + "第一题"）
      (/^[一二三四五六七八九十]+[、.]$/.test(text1.trim()) &&
        /^第[一二三四五六七八九十\d]+[题项]/.test(text2.trim()));

    // 严格限制文本长度，避免合并长内容
    const isShortText1 = text1.trim().length > 0 && text1.trim().length <= 10;
    const isShortText2 = text2.trim().length > 0 && text2.trim().length <= 15;

    // 排除明显的块级内容（包含句号、问号、感叹号或长文本）
    const isNotBlockContent =
      !text1.includes('。') &&
      !text1.includes('？') &&
      !text1.includes('！') &&
      !text2.includes('。') &&
      !text2.includes('？') &&
      !text2.includes('！') &&
      !text1.includes('给加点字') &&
      !text2.includes('给加点字') &&
      !text1.includes('同学们向') &&
      !text2.includes('同学们向') &&
      !text1.includes('下列') &&
      !text2.includes('下列') &&
      !text1.includes('请根据') &&
      !text2.includes('请根据') &&
      !text1.includes('形近字') &&
      !text2.includes('形近字');

    // 只有当满足所有条件时才合并
    const shouldMerge =
      isShortText1 &&
      isShortText2 &&
      isFlexInlinePattern &&
      isNotBlockContent;

    return shouldMerge;
  }

  /**
   * 处理文档格式优化
   * 包括设置默认行间距为1.5和处理文本对齐
   */
  private async processDocumentFormatting(xmlDoc: Document): Promise<boolean> {
    try {
      console.log('开始处理文档格式优化...');

      let processedCount = 0;

      // 1. 设置默认行间距为1.5
      const lineSpacingProcessed = this.setDefaultLineSpacing(xmlDoc);
      if (lineSpacingProcessed) {
        processedCount++;
        console.log('默认行间距设置完成');
      }

      // 2. 处理文本对齐
      const alignmentProcessed = this.processTextAlignment(xmlDoc);
      if (alignmentProcessed) {
        processedCount++;
        console.log('文本对齐处理完成');
      }

      console.log(`成功处理 ${processedCount} 个格式优化`);
      return processedCount > 0;
    } catch (error) {
      console.error('文档格式处理失败:', error);
      return false;
    }
  }

  /**
   * 设置默认行间距为1.5
   */
  private setDefaultLineSpacing(xmlDoc: Document): boolean {
    try {
      console.log('设置默认行间距为1.5...');

      // 查找所有段落
      const paragraphs = xmlDoc.getElementsByTagName('w:p');
      let processedCount = 0;

      for (let i = 0; i < paragraphs.length; i++) {
        const paragraph = paragraphs[i];

        // 获取或创建段落属性
        let pPr = paragraph.getElementsByTagName('w:pPr')[0];
        if (!pPr) {
          pPr = xmlDoc.createElement('w:pPr');
          paragraph.insertBefore(pPr, paragraph.firstChild);
        }

        // 检查是否已有行间距设置
        const existingSpacing = pPr.getElementsByTagName('w:spacing')[0];
        if (!existingSpacing) {
          // 创建行间距设置：1.5倍行距
          const spacing = xmlDoc.createElement('w:spacing');
          spacing.setAttribute('w:line', '360'); // 1.5 * 240 = 360 (240是单倍行距)
          spacing.setAttribute('w:lineRule', 'auto');
          pPr.appendChild(spacing);
          processedCount++;
        } else {
          // 检查现有行间距是否需要调整为1.5倍
          const currentLine = existingSpacing.getAttribute('w:line');
          if (currentLine !== '360') {
            existingSpacing.setAttribute('w:line', '360');
            existingSpacing.setAttribute('w:lineRule', 'auto');
            processedCount++;
          }
        }
      }

      console.log(`设置了 ${processedCount} 个段落的行间距`);
      return processedCount > 0;
    } catch (error) {
      console.error('设置行间距失败:', error);
      return false;
    }
  }

  /**
   * 处理文本对齐
   * 检测HTML中的text-align样式并应用到Word文档
   */
  private processTextAlignment(xmlDoc: Document): boolean {
    try {
      console.log('处理文本对齐...');

      // 查找所有段落
      const paragraphs = xmlDoc.getElementsByTagName('w:p');
      let processedCount = 0;

      for (let i = 0; i < paragraphs.length; i++) {
        const paragraph = paragraphs[i];
        const paragraphText = this.getParagraphText(paragraph);

        // 检查这个段落是否应该居中
        // 通过检查段落内容来判断是否是需要居中的内容
        if (this.shouldBeCentered(paragraphText)) {
          console.log(`设置段落居中: "${paragraphText.substring(0, 30)}..."`);

          // 获取或创建段落属性
          let pPr = paragraph.getElementsByTagName('w:pPr')[0];
          if (!pPr) {
            pPr = xmlDoc.createElement('w:pPr');
            paragraph.insertBefore(pPr, paragraph.firstChild);
          }

          // 检查是否已有对齐设置
          const existingJc = pPr.getElementsByTagName('w:jc')[0];
          if (!existingJc) {
            // 创建居中对齐设置
            const jc = xmlDoc.createElement('w:jc');
            jc.setAttribute('w:val', 'center');
            pPr.appendChild(jc);
            processedCount++;
          }
        }
      }

      console.log(`设置了 ${processedCount} 个段落的对齐方式`);
      return processedCount > 0;
    } catch (error) {
      console.error('处理文本对齐失败:', error);
      return false;
    }
  }

  /**
   * 判断段落是否应该居中
   * 基于内容特征来判断
   */
  private shouldBeCentered(text: string): boolean {
    // 检查是否是标题类内容（包含姓名、班级、考号等）
    const isHeaderInfo = /姓名|班级|考号|作业|考试|试卷/.test(text);

    // 检查是否是日期格式
    const isDate = /\d{4}年\d{1,2}月\d{1,2}日/.test(text);

    // 检查是否是标题格式（短文本且包含特定关键词）
    const isTitle = text.length < 50 && /阅读|访师|冒雪/.test(text);

    return isHeaderInfo || isDate || isTitle;
  }

  /**
   * 检测是否需要表格格式优化
   */
  private needsTableFormatting(): boolean {
    // 检查HTML中是否包含表格
    const hasTables = /<table[^>]*>/i.test(this.originalHtml);

    if (hasTables) {
      console.log('检测到表格，需要边框样式优化');
    }

    return hasTables;
  }

  /**
   * 处理表格格式优化
   * 确保表格使用默认单线边框
   */
  private async processTableFormatting(xmlDoc: Document): Promise<boolean> {
    try {
      console.log('开始处理表格边框优化...');

      // 查找所有表格
      const tables = xmlDoc.getElementsByTagName('w:tbl');
      let processedCount = 0;

      for (let i = 0; i < tables.length; i++) {
        const table = tables[i];

        // 处理表格属性
        const tableProcessed = this.processTableBorders(xmlDoc, table);
        if (tableProcessed) {
          processedCount++;
        }
      }

      console.log(`成功处理 ${processedCount} 个表格的边框样式`);
      return processedCount > 0;
    } catch (error) {
      console.error('表格格式处理失败:', error);
      return false;
    }
  }

  /**
   * 处理单个表格的边框样式
   * 确保使用默认单线边框
   */
  private processTableBorders(xmlDoc: Document, table: Element): boolean {
    try {
      // 获取或创建表格属性
      let tblPr = table.getElementsByTagName('w:tblPr')[0];
      if (!tblPr) {
        tblPr = xmlDoc.createElement('w:tblPr');
        table.insertBefore(tblPr, table.firstChild);
      }

      // 设置表格边框为默认单线边框
      let tblBorders = tblPr.getElementsByTagName('w:tblBorders')[0];
      if (!tblBorders) {
        tblBorders = xmlDoc.createElement('w:tblBorders');
        tblPr.appendChild(tblBorders);
      }

      // 清除现有边框设置
      while (tblBorders.firstChild) {
        tblBorders.removeChild(tblBorders.firstChild);
      }

      // 设置默认单线边框（上下左右）
      const borderTypes = [
        'top',
        'left',
        'bottom',
        'right',
        'insideH',
        'insideV',
      ];
      borderTypes.forEach(borderType => {
        const border = xmlDoc.createElement(`w:${borderType}`);
        border.setAttribute('w:val', 'single');
        border.setAttribute('w:sz', '4'); // 默认边框宽度
        border.setAttribute('w:space', '0');
        border.setAttribute('w:color', '000000'); // 黑色
        tblBorders.appendChild(border);
      });

      // 处理表格中的所有单元格
      const cells = table.getElementsByTagName('w:tc');
      for (let i = 0; i < cells.length; i++) {
        this.processTableCellBorders(xmlDoc, cells[i]);
      }

      console.log('表格边框样式已优化为默认单线边框');
      return true;
    } catch (error) {
      console.error('处理表格边框失败:', error);
      return false;
    }
  }

  /**
   * 处理单元格边框样式
   */
  private processTableCellBorders(xmlDoc: Document, cell: Element): void {
    try {
      // 获取或创建单元格属性
      let tcPr = cell.getElementsByTagName('w:tcPr')[0];
      if (!tcPr) {
        tcPr = xmlDoc.createElement('w:tcPr');
        cell.insertBefore(tcPr, cell.firstChild);
      }

      // 设置单元格边框为默认样式
      let tcBorders = tcPr.getElementsByTagName('w:tcBorders')[0];
      if (!tcBorders) {
        tcBorders = xmlDoc.createElement('w:tcBorders');
        tcPr.appendChild(tcBorders);
      }

      // 清除现有边框设置
      while (tcBorders.firstChild) {
        tcBorders.removeChild(tcBorders.firstChild);
      }

      // 设置默认单线边框
      const borderTypes = ['top', 'left', 'bottom', 'right'];
      borderTypes.forEach(borderType => {
        const border = xmlDoc.createElement(`w:${borderType}`);
        border.setAttribute('w:val', 'single');
        border.setAttribute('w:sz', '4'); // 默认边框宽度
        border.setAttribute('w:space', '0');
        border.setAttribute('w:color', '000000'); // 黑色
        tcBorders.appendChild(border);
      });
    } catch (error) {
      console.error('处理单元格边框失败:', error);
    }
  }

  /**
   * 检测是否有图片需要处理
   */
  private hasImages(): boolean {
    // 检查HTML中是否包含base64图片
    const hasBase64Images = /<img[^>]*src="data:image\/[^"]*"[^>]*>/i.test(
      this.originalHtml
    );

    if (hasBase64Images) {
      const imageCount = (
        this.originalHtml.match(/<img[^>]*src="data:image\/[^"]*"[^>]*>/gi) ||
        []
      ).length;
      console.log(`检测到 ${imageCount} 个base64图片需要处理`);
    }

    return hasBase64Images;
  }

  /**
   * 处理标记为行内显示的图片
   * 修改Word XML中的图片布局，使其能够与文本在同一行显示
   */
  private async processInlineImages(
    xmlDoc: Document,
    docxZip: JSZip
  ): Promise<void> {
    try {
      console.log('开始处理行内图片...');

      // 查找所有图片元素
      const drawingElements = xmlDoc.getElementsByTagName('w:drawing');

      // 提取HTML中标记为行内的图片信息
      const inlineImageMarkers = this.originalHtml.match(/data-inline-marker="([^"]*)"/gi) || [];

      console.log(`找到 ${drawingElements.length} 个Word图片元素`);
      console.log(`找到 ${inlineImageMarkers.length} 个行内图片标记`);

      // 遍历所有图片元素，修改其布局属性
      for (let i = 0; i < drawingElements.length && i < inlineImageMarkers.length; i++) {
        const drawing = drawingElements[i];

        // 查找图片的锚点设置
        const anchor = drawing.getElementsByTagName('wp:anchor')[0];
        const inline = drawing.getElementsByTagName('wp:inline')[0];

        if (anchor) {
          console.log(`图片 ${i + 1}: 发现锚点布局，转换为行内布局`);

          // 将锚点布局转换为行内布局
          this.convertAnchorToInline(drawing, anchor);
        } else if (inline) {
          console.log(`图片 ${i + 1}: 已经是行内布局，优化显示属性`);

          // 优化行内图片的显示属性
          this.optimizeInlineImage(inline);
        } else {
          console.log(`图片 ${i + 1}: 未找到布局信息，跳过处理`);
        }
      }

      console.log('行内图片处理完成');
    } catch (error) {
      console.error('处理行内图片时出错:', error);
    }
  }

  /**
   * 将锚点布局的图片转换为行内布局
   */
  private convertAnchorToInline(drawing: Element, anchor: Element): void {
    try {
      // 获取图片的基本信息
      const graphic = anchor.getElementsByTagName('a:graphic')[0];
      if (!graphic) {
        console.log('未找到图片内容，跳过转换');
        return;
      }

      // 创建新的行内元素
      const inline = drawing.ownerDocument.createElement('wp:inline');

      // 设置行内图片的基本属性
      inline.setAttribute('distT', '0');
      inline.setAttribute('distB', '0');
      inline.setAttribute('distL', '0');
      inline.setAttribute('distR', '0');

      // 复制图片内容
      const extent = anchor.getElementsByTagName('wp:extent')[0];
      if (extent) {
        const newExtent = extent.cloneNode(true);
        inline.appendChild(newExtent);
      }

      // 复制图片效果
      const effectExtent = anchor.getElementsByTagName('wp:effectExtent')[0];
      if (effectExtent) {
        const newEffectExtent = effectExtent.cloneNode(true);
        inline.appendChild(newEffectExtent);
      }

      // 创建文档属性
      const docPr = drawing.ownerDocument.createElement('wp:docPr');
      docPr.setAttribute('id', '1');
      docPr.setAttribute('name', 'Inline Picture');
      inline.appendChild(docPr);

      // 复制图片内容
      const newGraphic = graphic.cloneNode(true);
      inline.appendChild(newGraphic);

      // 替换锚点元素
      drawing.replaceChild(inline, anchor);

      console.log('成功将锚点布局转换为行内布局');
    } catch (error) {
      console.error('转换锚点布局时出错:', error);
    }
  }

  /**
   * 优化行内图片的显示属性
   */
  private optimizeInlineImage(inline: Element): void {
    try {
      // 设置图片与文本的间距
      inline.setAttribute('distT', '0');
      inline.setAttribute('distB', '0');
      inline.setAttribute('distL', '114300'); // 约4px的间距
      inline.setAttribute('distR', '114300'); // 约4px的间距

      // 查找并优化图片尺寸
      const extent = inline.getElementsByTagName('wp:extent')[0];
      if (extent) {
        const cx = parseInt(extent.getAttribute('cx') || '0');
        const cy = parseInt(extent.getAttribute('cy') || '0');

        // 如果图片太大，限制其高度以适应行高
        const maxHeight = 342900; // 约1.2em的高度
        if (cy > maxHeight) {
          const ratio = maxHeight / cy;
          const newCx = Math.round(cx * ratio);

          extent.setAttribute('cx', newCx.toString());
          extent.setAttribute('cy', maxHeight.toString());

          console.log(`调整图片尺寸: ${cx}x${cy} -> ${newCx}x${maxHeight}`);
        }
      }

      console.log('行内图片显示属性已优化');
    } catch (error) {
      console.error('优化行内图片时出错:', error);
    }
  }

  /**
   * 处理图片
   */
  private async processImages(
    xmlDoc: Document,
    docxZip: JSZip
  ): Promise<boolean> {
    try {
      console.log('开始处理图片...');

      // 检查html-to-docx是否已经处理了图片
      const drawingElements = xmlDoc.getElementsByTagName('w:drawing');

      // 统计HTML中的图片数量
      const htmlImageCount = (
        this.originalHtml.match(/<img[^>]*src="data:image\/[^"]*"[^>]*>/gi) ||
        []
      ).length;

      console.log(`HTML中有 ${htmlImageCount} 个base64图片`);
      console.log(`Word文档中有 ${drawingElements.length} 个图片元素`);

      // 检查是否有标记为行内显示的图片需要特殊处理
      const inlineImageMarkers = (
        this.originalHtml.match(/data-inline-marker="[^"]*"/gi) || []
      ).length;

      if (inlineImageMarkers > 0) {
        console.log(`发现 ${inlineImageMarkers} 个标记为行内显示的图片，需要特殊处理`);
        // 处理行内图片
        await this.processInlineImages(xmlDoc, docxZip);
      }

      if (drawingElements.length >= htmlImageCount) {
        console.log('html-to-docx已处理所有图片，无需额外处理');
        return false;
      }

      if (drawingElements.length > 0) {
        console.log(
          `html-to-docx已处理 ${drawingElements.length} 个图片，还有 ${
            htmlImageCount - drawingElements.length
          } 个图片未处理`
        );
      } else {
        console.log('html-to-docx未处理任何图片，开始手动处理...');
      }

      // 检查是否有图片在Word中不可见（可能是1x1像素图片）
      if (drawingElements.length >= htmlImageCount) {
        console.log('检查图片是否正确显示...');

        // 检查是否有极小的图片需要特殊处理
        const imageMatches =
          this.originalHtml.match(/<img[^>]*src="data:image\/[^"]*"[^>]*>/gi) ||
          [];
        let hasSmallImages = false;

        imageMatches.forEach((imgTag, index) => {
          const srcMatch = imgTag.match(/src="([^"]*)/);
          if (srcMatch) {
            const src = srcMatch[1];
            if (src.startsWith('data:image/') && src.length < 200) {
              console.log(`发现极小图片 ${index + 1}，检查Word中的显示效果`);
              hasSmallImages = true;
            }
          }
        });

        if (hasSmallImages) {
          console.log('检测到极小图片，这些图片在Word中可能不可见');
          console.log('建议：如果图片在Word中不显示，可能是1x1像素图片的限制');
        }
      } else {
        // 使用docx库手动处理未处理的图片
        const missingImageCount = htmlImageCount - drawingElements.length;
        console.log(
          `开始使用docx库处理 ${missingImageCount} 个未处理的图片...`
        );

        // 提取所有base64图片信息
        const imageMatches =
          this.originalHtml.match(/<img[^>]*src="data:image\/[^"]*"[^>]*>/gi) ||
          [];
        const processedImages = await this.processImagesWithDocx(
          imageMatches,
          xmlDoc,
          docxZip
        );

        if (processedImages > 0) {
          console.log(`成功使用docx库处理了 ${processedImages} 个图片`);
          return true;
        }
      }

      console.log('所有图片处理完成');
      return false;
    } catch (error) {
      console.error('图片处理失败:', error);
      return false;
    }
  }

  /**
   * 使用docx库处理图片
   */
  private async processImagesWithDocx(
    imageMatches: string[],
    xmlDoc: Document,
    docxZip: JSZip
  ): Promise<number> {
    try {
      let processedCount = 0;

      for (let i = 0; i < imageMatches.length; i++) {
        const imgTag = imageMatches[i];
        console.log(
          `处理图片 ${i + 1}/${imageMatches.length}: ${imgTag.substring(
            0,
            100
          )}...`
        );

        // 提取src属性
        const srcMatch = imgTag.match(/src="([^"]*)/);
        if (!srcMatch) {
          console.log(`图片 ${i + 1} 没有src属性，跳过`);
          continue;
        }

        const src = srcMatch[1];
        if (!src.startsWith('data:image/')) {
          console.log(`图片 ${i + 1} 不是base64图片，跳过`);
          continue;
        }

        // 解析base64数据
        const [mimeInfo, base64Data] = src.split(',');
        if (!base64Data) {
          console.log(`图片 ${i + 1} base64数据格式错误，跳过`);
          continue;
        }

        // 提取图片类型
        const mimeType = mimeInfo.split(';')[0].split(':')[1];

        // 提取图片尺寸
        const widthMatch = imgTag.match(/width="([^"]*)/);
        const heightMatch = imgTag.match(/height="([^"]*)/);
        const width = widthMatch ? parseInt(widthMatch[1]) : 100;
        const height = heightMatch ? parseInt(heightMatch[1]) : 100;

        console.log(
          `图片 ${
            i + 1
          } 类型: ${mimeType}, 尺寸: ${width}x${height}, 数据长度: ${
            base64Data.length
          }`
        );

        // 检查这个图片是否已经被html-to-docx处理了
        if (await this.isImageAlreadyProcessed(xmlDoc, base64Data)) {
          console.log(`图片 ${i + 1} 已被html-to-docx处理，跳过`);
          continue;
        }

        // 使用docx库插入图片
        const inserted = await this.insertImageWithDocx(
          xmlDoc,
          docxZip,
          base64Data,
          mimeType,
          width,
          height,
          i
        );
        if (inserted) {
          console.log(`图片 ${i + 1} 使用docx库插入成功`);
          processedCount++;
        } else {
          console.log(`图片 ${i + 1} 插入失败`);
        }
      }

      return processedCount;
    } catch (error) {
      console.error('使用docx库处理图片失败:', error);
      return 0;
    }
  }

  /**
   * 检查图片是否已经被html-to-docx处理
   */
  private async isImageAlreadyProcessed(
    xmlDoc: Document,
    base64Data: string
  ): Promise<boolean> {
    // 检查是否是极小的图片（1x1像素等），这种图片html-to-docx可能会忽略
    if (base64Data.length < 200) {
      console.log(
        `检测到极小图片（${base64Data.length}字符），html-to-docx可能已忽略`
      );
      return true; // 认为已被处理（实际是被忽略了）
    }
    return false;
  }

  /**
   * 使用docx库插入图片（备用方案）
   * 注意：由于html-to-docx现在能够处理所有图片，这个方法主要作为备用
   */
  private async insertImageWithDocx(
    xmlDoc: Document,
    docxZip: JSZip,
    base64Data: string,
    mimeType: string,
    width: number,
    height: number,
    index: number
  ): Promise<boolean> {
    try {
      console.log(`开始使用docx库插入图片 ${index + 1}...`);

      // 实际的图片插入需要复杂的实现，包括：
      // 1. 解码base64数据并创建图片文件
      // 2. 添加图片文件到docx ZIP包中
      // 3. 创建关系文件
      // 4. 在XML中插入正确的图片元素
      //
      // 由于html-to-docx现在能够处理所有图片，这个复杂的实现暂时不需要

      console.log(`图片 ${index + 1} 处理跳过（html-to-docx已处理）`);
      return false;
    } catch (error) {
      console.error(`插入图片 ${index + 1} 失败:`, error);
      return false;
    }
  }

  // 扩展点：添加其他特殊样式处理方法
  // private hasSpecialFonts(): boolean { return false; }
  // private processSpecialFonts(xmlDoc: Document): Promise<boolean> { return Promise.resolve(false); }
  // private hasSpecialColors(): boolean { return false; }
  // private processSpecialColors(xmlDoc: Document): Promise<boolean> { return Promise.resolve(false); }
}

/**
 * 便捷函数：对Word文档应用XML补丁
 * 简洁接口：自动检测并处理特殊样式
 */
export async function patchWordDocument(
  docxBuffer: Buffer,
  originalHtml: string
): Promise<Buffer> {
  const patcher = new WordXmlPatcherV2(docxBuffer, originalHtml);
  return await patcher.applyPatches();
}
